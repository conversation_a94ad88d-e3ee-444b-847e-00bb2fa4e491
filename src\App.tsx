import React, { useState } from 'react';
import { Layout, Menu, Typo<PERSON>, <PERSON><PERSON>, Card, Row, Col, Space } from 'antd';
import {
  BookOutlined,
  SoundOutlined,
  EditOutlined,
  MessageOutlined,
  UnorderedListOutlined,
  SettingOutlined,
  HomeOutlined
} from '@ant-design/icons';
import VocabularyManager from './components/Vocabulary/VocabularyManager';
import ListeningPlayer from './components/Listening/ListeningPlayer';
import ReadingPassage from './components/Reading/ReadingPassage';
import WritingTeacherDemo from './components/Writing/WritingTeacherDemo';
import SpeakingTeacherDemo from './components/Speaking/SpeakingTeacherDemo';
import NavigationBreadcrumbs from './components/Common/NavigationBreadcrumbs';
import { generateVocabularyHandout, downloadTextFile } from './components/Common/ExportUtils';
import { WRITING_TASKS } from './data/writingTasks';
import { SPEAKING_TASKS } from './data/speakingTasks';
import { VocabularyProvider, useVocabulary } from './contexts/VocabularyContext';
import './App.css';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

// Topic categories for IELTS
const IELTS_TOPICS = [
  'Environment',
  'Technology',
  'Education',
  'Health & Medicine',
  'Travel & Tourism',
  'Work & Career',
  'Society & Culture',
  'Science & Research',
  'Media & Communication',
  'Arts & Entertainment'
];

const AppContent: React.FC = () => {
  const [selectedSkill, setSelectedSkill] = useState<string>('');
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [collapsed, setCollapsed] = useState(false);
  const [presentationMode, setPresentationMode] = useState(false);
  const { getVocabularyByTopic } = useVocabulary();

  // Handle presentation mode toggle
  const togglePresentationMode = () => {
    setPresentationMode(!presentationMode);

    // Request fullscreen when entering presentation mode
    if (!presentationMode) {
      if (document.documentElement.requestFullscreen) {
        document.documentElement.requestFullscreen().catch(err => {
          console.log('Error attempting to enable fullscreen:', err);
        });
      }
    } else {
      // Exit fullscreen when leaving presentation mode
      if (document.fullscreenElement && document.exitFullscreen) {
        document.exitFullscreen().catch(err => {
          console.log('Error attempting to exit fullscreen:', err);
        });
      }
    }
  };

  const skillMenuItems = [
    {
      key: 'listening',
      icon: <SoundOutlined />,
      label: 'Listening',
    },
    {
      key: 'reading',
      icon: <BookOutlined />,
      label: 'Reading',
    },
    {
      key: 'writing',
      icon: <EditOutlined />,
      label: 'Writing',
    },
    {
      key: 'speaking',
      icon: <MessageOutlined />,
      label: 'Speaking',
    },
    {
      key: 'vocabulary',
      icon: <UnorderedListOutlined />,
      label: 'Vocabulary',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
  ];

  // Sample data for demonstration
  const sampleTranscript = [
    {
      id: '1',
      startTime: 0,
      endTime: 15,
      text: "Welcome to today's discussion about environmental sustainability. We'll be exploring various approaches to reducing carbon emissions.",
      speaker: 'Host'
    },
    {
      id: '2',
      startTime: 15,
      endTime: 30,
      text: "That's a fascinating topic. I believe technology plays a crucial role in achieving sustainable development goals.",
      speaker: 'Guest'
    }
  ];

  const sampleReadingQuestions = [
    {
      id: '1',
      question: 'What is the main topic of the passage?',
      type: 'multiple-choice' as const,
      options: ['Technology', 'Environment', 'Education', 'Health'],
      answer: 'Environment',
      explanation: 'The passage focuses on environmental sustainability and carbon emissions.',
      answerLocation: {
        startText: 'Environmental sustainability',
        endText: 'carbon footprint'
      }
    }
  ];

  const handleVocabularyExport = (vocabularyList: any[]) => {
    const content = generateVocabularyHandout(vocabularyList, selectedTopic);
    const filename = `IELTS_Vocabulary_${selectedTopic || 'All'}_${new Date().toISOString().split('T')[0]}.txt`;
    downloadTextFile(content, filename);
  };

  // Generate breadcrumb items based on current navigation state
  const getBreadcrumbItems = () => {
    const items = [
      {
        title: 'Home',
        icon: <HomeOutlined />,
        onClick: () => {
          setSelectedSkill('');
          setSelectedTopic('');
        }
      }
    ];

    if (selectedSkill) {
      items.push({
        title: selectedSkill.charAt(0).toUpperCase() + selectedSkill.slice(1),
        onClick: selectedTopic ? () => setSelectedTopic('') : () => {},
        icon: <SoundOutlined />
      });
    }

    if (selectedTopic) {
      items.push({
        title: selectedTopic,
        icon: <BookOutlined />,
        onClick: () => {} // Current page, no action needed
      });
    }

    return items;
  };

  // Handle back navigation
  const handleBack = () => {
    if (selectedTopic) {
      setSelectedTopic('');
    } else if (selectedSkill) {
      setSelectedSkill('');
    }
  };

  const renderTopicSelection = () => (
    <div style={{ padding: '20px' }}>
      <Title level={2}>Select Topic for {selectedSkill.charAt(0).toUpperCase() + selectedSkill.slice(1)}</Title>
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '16px',
        marginTop: '20px'
      }}>
        {IELTS_TOPICS.map(topic => (
          <Card
            key={topic}
            hoverable
            onClick={() => setSelectedTopic(topic)}
            style={{
              cursor: 'pointer',
              border: selectedTopic === topic ? '2px solid #1890ff' : '1px solid #d9d9d9'
            }}
          >
            <Card.Meta
              title={topic}
              description={`${selectedSkill.charAt(0).toUpperCase() + selectedSkill.slice(1)} exercises and vocabulary for ${topic.toLowerCase()}`}
            />
          </Card>
        ))}
      </div>
    </div>
  );

  const renderContent = () => {
    if (!selectedSkill) {
      return (
        <div style={{
          padding: '40px',
          textAlign: 'center',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <Title level={1}>IELTS Teaching Platform</Title>
          <Text style={{ fontSize: '18px', marginBottom: '30px' }}>
            Interactive classroom tool for IELTS preparation with topic-based organization
          </Text>
          <div style={{ marginTop: '20px' }}>
            <Text style={{ fontSize: '16px' }}>
              Select a skill from the sidebar to begin teaching
            </Text>
          </div>
        </div>
      );
    }

    if (!selectedTopic) {
      return renderTopicSelection();
    }

    // Render specific skill components based on selection
    switch (selectedSkill) {
      case 'listening':
        return (
          <ListeningPlayer
            title={`Listening Practice - ${selectedTopic}`}
            topic={selectedTopic}
            transcript={sampleTranscript}
            questions={['What is the main topic discussed?', 'Who are the speakers?']}
            answers={['Environmental sustainability', 'Host and Guest']}
            vocabularyHints={getVocabularyByTopic(selectedTopic)}
          />
        );

      case 'reading':
        return (
          <ReadingPassage
            title={`Reading Practice - ${selectedTopic}`}
            topic={selectedTopic}
            passage="Environmental sustainability has become one of the most pressing issues of our time. As global temperatures continue to rise and natural resources become increasingly scarce, governments and organizations worldwide are implementing various strategies to reduce their carbon footprint. Sustainable development involves meeting the needs of the present without compromising the ability of future generations to meet their own needs. This concept encompasses economic, social, and environmental dimensions, requiring a holistic approach to problem-solving."
            vocabularyHints={getVocabularyByTopic(selectedTopic)}
            questions={sampleReadingQuestions}
          />
        );

      case 'vocabulary':
        return (
          <VocabularyManager
            selectedTopic={selectedTopic}
            onExport={handleVocabularyExport}
          />
        );

      case 'writing':
        const writingTasks = WRITING_TASKS[selectedTopic] || [];
        const selectedWritingTask = writingTasks[0]; // Default to first task

        if (!selectedWritingTask) {
          return (
            <div style={{ padding: '40px', textAlign: 'center' }}>
              <Title level={2}>Writing - {selectedTopic}</Title>
              <Text style={{ fontSize: '18px' }}>
                No writing tasks available for this topic yet.
              </Text>
            </div>
          );
        }

        return (
          <WritingTeacherDemo
            task={selectedWritingTask}
            topic={selectedTopic}
            vocabularyHints={getVocabularyByTopic(selectedTopic)}
          />
        );

      case 'speaking':
        const speakingTasks = SPEAKING_TASKS[selectedTopic] || [];
        const selectedSpeakingTask = speakingTasks[0]; // Default to first task (Part 1)

        if (!selectedSpeakingTask) {
          return (
            <div style={{ padding: '40px', textAlign: 'center' }}>
              <Title level={2}>Speaking - {selectedTopic}</Title>
              <Text style={{ fontSize: '18px' }}>
                No speaking tasks available for this topic yet.
              </Text>
            </div>
          );
        }

        return (
          <SpeakingTeacherDemo
            task={selectedSpeakingTask}
            topic={selectedTopic}
            vocabularyHints={getVocabularyByTopic(selectedTopic)}
          />
        );

      default:
        return renderTopicSelection();
    }
  };

  return (
    <Layout style={{ height: '100vh' }} className={presentationMode ? 'presentation-mode' : ''}>
      {!presentationMode && (
        <Sider
          collapsible
          collapsed={collapsed}
          onCollapse={setCollapsed}
          width={250}
          style={{ background: '#001529' }}
        >
        <div style={{
          height: '64px',
          margin: '16px',
          background: 'rgba(255, 255, 255, 0.2)',
          borderRadius: '6px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Title level={4} style={{ color: 'white', margin: 0 }}>
            {collapsed ? 'IELTS' : 'IELTS Platform'}
          </Title>
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[selectedSkill]}
          items={skillMenuItems}
          onClick={({ key }) => {
            setSelectedSkill(key);
            setSelectedTopic(''); // Reset topic when changing skill
          }}
        />
        </Sider>
      )}

      <Layout>
        <Header style={{
          background: '#fff',
          padding: '0 24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Title level={3} style={{ margin: 0 }}>
            {selectedSkill === 'settings' ? 'Settings' :
             selectedTopic ? `${selectedSkill.charAt(0).toUpperCase() + selectedSkill.slice(1)} - ${selectedTopic}` :
             selectedSkill ? `${selectedSkill.charAt(0).toUpperCase() + selectedSkill.slice(1)}` : 'Welcome'}
          </Title>
          <div>
            <Button
              type={presentationMode ? "default" : "primary"}
              size="large"
              onClick={togglePresentationMode}
              style={{
                fontSize: presentationMode ? '18px' : '16px',
                height: presentationMode ? '50px' : '40px',
                minWidth: '200px'
              }}
            >
              {presentationMode ? 'Exit' : 'Enter'} Presentation Mode
            </Button>
          </div>
        </Header>

        <Content style={{
          margin: 0,
          background: '#fff',
          overflow: 'auto'
        }}>
          {/* Show breadcrumbs when not on home page */}
          {(selectedSkill || selectedTopic) && (
            <div style={{
              padding: presentationMode ? '20px 40px 0' : '16px 24px 0',
              background: presentationMode ? 'transparent' : '#fff'
            }}>
              <NavigationBreadcrumbs
                items={getBreadcrumbItems()}
                onBack={(selectedSkill || selectedTopic) ? handleBack : undefined}
                presentationMode={presentationMode}
              />
            </div>
          )}
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  );
};

const App: React.FC = () => {
  return (
    <VocabularyProvider>
      <AppContent />
    </VocabularyProvider>
  );
};

export default App;
