import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Space, Row, Col, Divider, Tag, Alert, Steps } from 'antd';
import {
  SoundOutlined,
  PlayCircleOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BulbOutlined,
  CheckCircleOutlined,
  ArrowRightOutlined,
  ClockCircleOutlined,
  BookOutlined,
  PauseCircleOutlined,
  StopOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

interface SpeakingTask {
  id: string;
  part: 1 | 2 | 3;
  title: string;
  instructions: string;
  questions: string[];
  preparationTime?: number;
  speakingTime: number;
  tips?: string[];
  sampleResponses?: string[];
  speakingStrategies?: string[];
}

interface VocabularyHint {
  word: string;
  definition: string;
  example?: string;
}

interface SpeakingTeacherDemoProps {
  task: SpeakingTask;
  topic: string;
  vocabularyHints?: VocabularyHint[];
}

const SpeakingTeacherDemo: React.FC<SpeakingTeacherDemoProps> = ({
  task,
  topic,
  vocabularyHints = []
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [showSampleResponse, setShowSampleResponse] = useState(false);
  const [showVocabulary, setShowVocabulary] = useState(false);
  const [currentStrategyStep, setCurrentStrategyStep] = useState(0);
  const [revealedSentences, setRevealedSentences] = useState<number[]>([]);
  const [timeRemaining, setTimeRemaining] = useState(task.speakingTime);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [timerStarted, setTimerStarted] = useState(false);
  const [currentPhase, setCurrentPhase] = useState<'preparation' | 'speaking'>('preparation');

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerRunning && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            setIsTimerRunning(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, timeRemaining]);

  const speakingStrategies = task.speakingStrategies || [
    "Read the question carefully and identify key points",
    "Think of relevant examples and experiences",
    "Structure your response with clear beginning, middle, and end",
    "Use appropriate vocabulary and varied sentence structures",
    "Speak clearly and maintain natural pace",
    "Extend your answers with explanations and examples"
  ];

  const startTimer = (phase: 'preparation' | 'speaking') => {
    setCurrentPhase(phase);
    setTimeRemaining(phase === 'preparation' ? (task.preparationTime || 60) : task.speakingTime);
    setIsTimerRunning(true);
    setTimerStarted(true);
  };

  const pauseTimer = () => {
    setIsTimerRunning(false);
  };

  const resetTimer = () => {
    setIsTimerRunning(false);
    setTimerStarted(false);
    setCurrentPhase('preparation');
    setTimeRemaining(task.preparationTime || task.speakingTime);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getTimerColor = () => {
    const totalTime = currentPhase === 'preparation' ? (task.preparationTime || 60) : task.speakingTime;
    const percentage = timeRemaining / totalTime;
    if (percentage > 0.5) return '#52c41a';
    if (percentage > 0.25) return '#faad14';
    return '#ff4d4f';
  };

  const sampleResponses = task.sampleResponses || task.questions.map((question, index) => 
    `This is a sample response for question ${index + 1}. ${task.part === 1 ? 
      'I would say that this topic is quite interesting to me. For example, in my daily life, I often encounter situations related to this. I think it\'s important because it affects many aspects of our lives.' :
      task.part === 2 ?
      'I\'d like to talk about a specific experience I had. It happened about two years ago when I was... The situation was quite memorable because... What made it special was... Overall, I learned a lot from this experience.' :
      'That\'s a thought-provoking question. I believe there are several factors to consider. On one hand, we can see that... On the other hand, it\'s also important to recognize that... In my opinion, the key issue is...'
    }`
  );

  const currentResponse = sampleResponses[currentQuestionIndex] || '';
  const responseSentences = currentResponse.split('. ').filter(s => s.trim());

  const revealNextSentence = () => {
    if (revealedSentences.length < responseSentences.length) {
      setRevealedSentences([...revealedSentences, revealedSentences.length]);
    }
  };

  const resetReveal = () => {
    setRevealedSentences([]);
  };

  const nextQuestion = () => {
    if (currentQuestionIndex < task.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      resetReveal();
    }
  };

  const previousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      resetReveal();
    }
  };

  const highlightVocabulary = (text: string) => {
    if (!vocabularyHints.length) return text;
    
    let highlightedText = text;
    vocabularyHints.forEach(hint => {
      const regex = new RegExp(`\\b(${hint.word})\\b`, 'gi');
      highlightedText = highlightedText.replace(regex, 
        `<span class="vocabulary-word" style="background-color: #fff2e8; color: #d46b08; font-weight: 500; padding: 2px 4px; border-radius: 3px; cursor: pointer;" data-word="${hint.word}">$1</span>`
      );
    });
    return highlightedText;
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div style={{ padding: '20px' }}>
      {/* Header */}
      <Card style={{ marginBottom: '20px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              <SoundOutlined /> IELTS Speaking Part {task.part} - {topic} (Teacher Demo)
            </Title>
            <Text type="secondary">{task.title}</Text>
          </Col>
          <Col>
            <Space size="large">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: getTimerColor() }}>
                  <ClockCircleOutlined /> {formatTime(timeRemaining)}
                </div>
                <Text type="secondary">
                  {currentPhase === 'preparation' ? 'Preparation Time' : 'Speaking Time'}
                </Text>
                <div style={{ marginTop: '8px' }}>
                  <Space>
                    {!timerStarted ? (
                      <Space>
                        {task.preparationTime && (
                          <Button
                            type="primary"
                            icon={<PlayCircleOutlined />}
                            onClick={() => startTimer('preparation')}
                            size="small"
                          >
                            Start Prep
                          </Button>
                        )}
                        <Button
                          type="primary"
                          icon={<PlayCircleOutlined />}
                          onClick={() => startTimer('speaking')}
                          size="small"
                        >
                          Start Speaking
                        </Button>
                      </Space>
                    ) : (
                      <>
                        <Button
                          type={isTimerRunning ? "default" : "primary"}
                          icon={isTimerRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                          onClick={isTimerRunning ? pauseTimer : () => setIsTimerRunning(true)}
                          size="small"
                        >
                          {isTimerRunning ? 'Pause' : 'Resume'}
                        </Button>
                        <Button
                          icon={<StopOutlined />}
                          onClick={resetTimer}
                          size="small"
                        >
                          Reset
                        </Button>
                        {currentPhase === 'preparation' && task.preparationTime && (
                          <Button
                            type="primary"
                            onClick={() => startTimer('speaking')}
                            size="small"
                          >
                            Start Speaking
                          </Button>
                        )}
                      </>
                    )}
                  </Space>
                </div>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      <Row gutter={24}>
        {/* Left Column - Instructions and Strategies */}
        <Col span={12}>
          {/* Task Instructions */}
          <Card 
            title={`Speaking Part ${task.part} - Instructions`}
            style={{ marginBottom: '20px' }}
          >
            <Paragraph style={{ fontSize: '16px', lineHeight: '1.6' }}>
              {task.instructions}
            </Paragraph>
            
            {task.part === 2 && task.preparationTime && (
              <Alert
                message="Preparation Time"
                description={`You have ${formatTime(task.preparationTime)} to prepare. You can make notes on the question paper.`}
                type="info"
                showIcon
                style={{ marginTop: '16px' }}
              />
            )}
          </Card>

          {/* Speaking Strategies */}
          <Card 
            title="Speaking Strategies"
            extra={
              <Button 
                type="primary"
                icon={<ArrowRightOutlined />}
                onClick={() => setCurrentStrategyStep(Math.min(currentStrategyStep + 1, speakingStrategies.length - 1))}
                disabled={currentStrategyStep >= speakingStrategies.length - 1}
                size="small"
              >
                Next Strategy
              </Button>
            }
            style={{ marginBottom: '20px' }}
          >
            <Steps
              direction="vertical"
              current={currentStrategyStep}
              size="small"
            >
              {speakingStrategies.map((strategy, index) => (
                <Step
                  key={index}
                  title={`Strategy ${index + 1}`}
                  description={strategy}
                  icon={index <= currentStrategyStep ? <CheckCircleOutlined /> : undefined}
                />
              ))}
            </Steps>
            <div style={{ marginTop: '16px', textAlign: 'center' }}>
              <Button 
                onClick={() => setCurrentStrategyStep(0)}
                icon={<BulbOutlined />}
                size="small"
              >
                Reset Strategies
              </Button>
            </div>
          </Card>

          {/* Tips */}
          {task.tips && task.tips.length > 0 && (
            <Card 
              title="Speaking Tips"
              style={{ marginBottom: '20px' }}
            >
              <ul>
                {task.tips.map((tip, index) => (
                  <li key={index} style={{ marginBottom: '8px' }}>
                    <Text>{tip}</Text>
                  </li>
                ))}
              </ul>
            </Card>
          )}
        </Col>

        {/* Right Column - Questions and Sample Responses */}
        <Col span={12}>
          {/* Question Navigation */}
          <Card 
            title={`Question ${currentQuestionIndex + 1} of ${task.questions.length}`}
            extra={
              <Space>
                <Button 
                  onClick={previousQuestion}
                  disabled={currentQuestionIndex === 0}
                  size="small"
                >
                  Previous
                </Button>
                <Button 
                  onClick={nextQuestion}
                  disabled={currentQuestionIndex >= task.questions.length - 1}
                  size="small"
                >
                  Next
                </Button>
              </Space>
            }
            style={{ marginBottom: '20px' }}
          >
            <div style={{ 
              padding: '20px', 
              backgroundColor: '#f0f2f5', 
              borderRadius: '8px',
              marginBottom: '16px'
            }}>
              <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
                {task.questions[currentQuestionIndex]}
              </Title>
            </div>
            
            {task.part === 2 && (
              <Alert
                message="Long Turn (2 minutes)"
                description="Speak for 1-2 minutes on this topic. The examiner will stop you when time is up."
                type="info"
                showIcon
              />
            )}
          </Card>

          {/* Sample Response */}
          <Card 
            title="Sample Response Demonstration"
            extra={
              <Space>
                <Button 
                  type="text"
                  icon={showSampleResponse ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  onClick={() => setShowSampleResponse(!showSampleResponse)}
                  size="small"
                >
                  {showSampleResponse ? 'Hide' : 'Show'} Full Response
                </Button>
              </Space>
            }
            style={{ marginBottom: '20px' }}
          >
            {showSampleResponse ? (
              <div 
                style={{ 
                  fontSize: '16px', 
                  lineHeight: '1.8',
                  backgroundColor: '#fafafa',
                  padding: '20px',
                  borderRadius: '8px'
                }}
                dangerouslySetInnerHTML={{ __html: highlightVocabulary(currentResponse) }}
              />
            ) : (
              <div>
                <Alert
                  message="Reveal Sample Response Step by Step"
                  description="Click 'Reveal Next Sentence' to show the sample response sentence by sentence for classroom demonstration."
                  type="info"
                  showIcon
                  style={{ marginBottom: '16px' }}
                />
                
                <Space style={{ marginBottom: '16px' }}>
                  <Button 
                    type="primary"
                    onClick={revealNextSentence}
                    disabled={revealedSentences.length >= responseSentences.length}
                  >
                    Reveal Next Sentence ({revealedSentences.length + 1}/{responseSentences.length})
                  </Button>
                  <Button onClick={resetReveal}>
                    Reset
                  </Button>
                </Space>

                <div style={{ 
                  fontSize: '16px', 
                  lineHeight: '1.8',
                  backgroundColor: '#fafafa',
                  padding: '20px',
                  borderRadius: '8px',
                  minHeight: '200px'
                }}>
                  {revealedSentences.map(index => (
                    <span key={index}>
                      <span 
                        dangerouslySetInnerHTML={{ 
                          __html: highlightVocabulary(responseSentences[index] + (index < responseSentences.length - 1 ? '. ' : '')) 
                        }} 
                      />
                    </span>
                  ))}
                </div>
              </div>
            )}
          </Card>

          {/* Vocabulary Helper */}
          {vocabularyHints.length > 0 && (
            <Card 
              title="Key Vocabulary"
              extra={
                <Button 
                  type="text"
                  icon={showVocabulary ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  onClick={() => setShowVocabulary(!showVocabulary)}
                  size="small"
                >
                  {showVocabulary ? 'Hide' : 'Show'}
                </Button>
              }
            >
              {showVocabulary && (
                <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {vocabularyHints.map((hint, index) => (
                    <div key={index} style={{ marginBottom: '12px', padding: '8px', backgroundColor: '#f9f9f9', borderRadius: '4px' }}>
                      <Text strong style={{ color: '#1890ff' }}>{hint.word}</Text>
                      <br />
                      <Text style={{ fontSize: '14px' }}>{hint.definition}</Text>
                      {hint.example && (
                        <>
                          <br />
                          <Text italic style={{ fontSize: '13px', color: '#666' }}>
                            Example: {hint.example}
                          </Text>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default SpeakingTeacherDemo;
