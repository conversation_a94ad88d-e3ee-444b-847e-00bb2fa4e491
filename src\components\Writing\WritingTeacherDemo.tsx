import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Space, Row, Col, Divider, Tag, Steps, Image, Alert } from 'antd';
import {
  EditOutlined,
  FileTextOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  BookOutlined,
  BulbOutlined,
  CheckCircleOutlined,
  ArrowRightOutlined,
  ClockCircleOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

interface WritingTask {
  id: string;
  type: 'task1' | 'task2';
  title: string;
  prompt: string;
  instructions: string;
  timeLimit: number;
  wordLimit: {
    min: number;
    max: number;
  };
  tips?: string[];
  sampleAnswer?: string;
  writingSteps?: string[];
  imageUrl?: string; // For Task 1 visual data
}

interface VocabularyHint {
  word: string;
  definition: string;
  example?: string;
}

interface WritingTeacherDemoProps {
  task: WritingTask;
  topic: string;
  vocabularyHints?: VocabularyHint[];
}

const WritingTeacherDemo: React.FC<WritingTeacherDemoProps> = ({
  task,
  topic,
  vocabularyHints = []
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [showSampleAnswer, setShowSampleAnswer] = useState(false);
  const [showVocabulary, setShowVocabulary] = useState(false);
  const [revealedParagraphs, setRevealedParagraphs] = useState<number[]>([]);
  const [timeRemaining, setTimeRemaining] = useState(task.timeLimit * 60);
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [timerStarted, setTimerStarted] = useState(false);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isTimerRunning && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            setIsTimerRunning(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isTimerRunning, timeRemaining]);

  const writingSteps = task.writingSteps || [
    "Analyze the task and identify key requirements",
    "Plan your essay structure and main points",
    "Write an engaging introduction",
    "Develop body paragraphs with supporting evidence",
    "Write a strong conclusion",
    "Review and edit your work"
  ];

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const startTimer = () => {
    setIsTimerRunning(true);
    setTimerStarted(true);
  };

  const pauseTimer = () => {
    setIsTimerRunning(false);
  };

  const resetTimer = () => {
    setIsTimerRunning(false);
    setTimerStarted(false);
    setTimeRemaining(task.timeLimit * 60);
  };

  const getTimerColor = () => {
    const percentage = timeRemaining / (task.timeLimit * 60);
    if (percentage > 0.5) return '#52c41a';
    if (percentage > 0.25) return '#faad14';
    return '#ff4d4f';
  };

  const sampleAnswer = task.sampleAnswer || `
    [Introduction]
    The ${task.type === 'task1' ? 'chart/graph/table' : 'topic'} presents important information about ${topic.toLowerCase()}. This essay will ${task.type === 'task1' ? 'summarize the key features and trends shown in the data' : 'discuss both perspectives and provide a personal viewpoint'}.

    [Body Paragraph 1]
    ${task.type === 'task1' ? 
      'The most striking feature of the data is the significant trend observed across the time period. The figures show a clear pattern that demonstrates...' :
      'On one hand, proponents of this view argue that... This perspective is supported by several compelling reasons...'
    }

    [Body Paragraph 2]
    ${task.type === 'task1' ? 
      'Another notable aspect of the data reveals contrasting patterns between different categories. Specifically, while some areas showed growth, others demonstrated...' :
      'Conversely, those who oppose this viewpoint contend that... Their arguments are based on practical considerations such as...'
    }

    [Conclusion]
    ${task.type === 'task1' ? 
      'In summary, the data clearly illustrates the main trends and patterns in the given time period, highlighting both similarities and differences across the categories presented.' :
      'In conclusion, while both perspectives have merit, I believe that a balanced approach is most effective. This would involve...'
    }
  `;

  const sampleParagraphs = sampleAnswer.split('\n\n').filter(p => p.trim());

  const revealNextParagraph = () => {
    if (revealedParagraphs.length < sampleParagraphs.length) {
      setRevealedParagraphs([...revealedParagraphs, revealedParagraphs.length]);
    }
  };

  const resetReveal = () => {
    setRevealedParagraphs([]);
  };

  const highlightVocabulary = (text: string) => {
    if (!vocabularyHints.length) return text;
    
    let highlightedText = text;
    vocabularyHints.forEach(hint => {
      const regex = new RegExp(`\\b(${hint.word})\\b`, 'gi');
      highlightedText = highlightedText.replace(regex, 
        `<span class="vocabulary-word" style="background-color: #fff2e8; color: #d46b08; font-weight: 500; padding: 2px 4px; border-radius: 3px; cursor: pointer;" data-word="${hint.word}">$1</span>`
      );
    });
    return highlightedText;
  };

  return (
    <div style={{ padding: '20px' }}>
      {/* Header */}
      <Card style={{ marginBottom: '20px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              <EditOutlined /> IELTS Writing {task.type.toUpperCase()} - {topic} (Teacher Demo)
            </Title>
            <Text type="secondary">{task.title}</Text>
          </Col>
          <Col>
            <Space size="large">
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#1890ff' }}>
                  <FileTextOutlined /> {task.wordLimit.min}-{task.wordLimit.max} words
                </div>
                <Text type="secondary">Target Length</Text>
              </div>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: getTimerColor() }}>
                  <ClockCircleOutlined /> {formatTime(timeRemaining)}
                </div>
                <Text type="secondary">Time Remaining</Text>
                <div style={{ marginTop: '8px' }}>
                  <Space>
                    {!timerStarted ? (
                      <Button
                        type="primary"
                        icon={<PlayCircleOutlined />}
                        onClick={startTimer}
                        size="small"
                      >
                        Start Timer
                      </Button>
                    ) : (
                      <>
                        <Button
                          type={isTimerRunning ? "default" : "primary"}
                          icon={isTimerRunning ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                          onClick={isTimerRunning ? pauseTimer : startTimer}
                          size="small"
                        >
                          {isTimerRunning ? 'Pause' : 'Resume'}
                        </Button>
                        <Button
                          icon={<StopOutlined />}
                          onClick={resetTimer}
                          size="small"
                        >
                          Reset
                        </Button>
                      </>
                    )}
                  </Space>
                </div>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      <Row gutter={24}>
        {/* Left Column - Task and Instructions */}
        <Col span={12}>
          {/* Task Prompt */}
          <Card 
            title={`Writing ${task.type.toUpperCase()} - Task Description`}
            style={{ marginBottom: '20px' }}
          >
            {task.type === 'task1' && task.imageUrl && (
              <div style={{ marginBottom: '16px', textAlign: 'center' }}>
                <Image
                  src={task.imageUrl}
                  alt="Task 1 Visual Data"
                  style={{ maxWidth: '100%', maxHeight: '300px' }}
                  placeholder={
                    <div style={{ 
                      height: '200px', 
                      backgroundColor: '#f5f5f5', 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'center',
                      border: '2px dashed #d9d9d9'
                    }}>
                      <Text type="secondary">Chart/Graph/Table Image Area</Text>
                    </div>
                  }
                />
              </div>
            )}
            <Paragraph style={{ fontSize: '16px', lineHeight: '1.6' }}>
              {task.prompt}
            </Paragraph>
            <Alert
              message="Instructions"
              description={task.instructions}
              type="info"
              showIcon
              style={{ marginTop: '16px' }}
            />
          </Card>

          {/* Writing Steps */}
          <Card 
            title="Step-by-Step Writing Process"
            extra={
              <Button 
                type="primary"
                icon={<ArrowRightOutlined />}
                onClick={() => setCurrentStep(Math.min(currentStep + 1, writingSteps.length - 1))}
                disabled={currentStep >= writingSteps.length - 1}
                size="small"
              >
                Next Step
              </Button>
            }
            style={{ marginBottom: '20px' }}
          >
            <Steps
              direction="vertical"
              current={currentStep}
              size="small"
            >
              {writingSteps.map((step, index) => (
                <Step
                  key={index}
                  title={`Step ${index + 1}`}
                  description={step}
                  icon={index <= currentStep ? <CheckCircleOutlined /> : undefined}
                />
              ))}
            </Steps>
            <div style={{ marginTop: '16px', textAlign: 'center' }}>
              <Button 
                onClick={() => setCurrentStep(0)}
                icon={<BulbOutlined />}
                size="small"
              >
                Reset Steps
              </Button>
            </div>
          </Card>

          {/* Tips */}
          {task.tips && task.tips.length > 0 && (
            <Card 
              title="Writing Tips"
              style={{ marginBottom: '20px' }}
            >
              <ul>
                {task.tips.map((tip, index) => (
                  <li key={index} style={{ marginBottom: '8px' }}>
                    <Text>{tip}</Text>
                  </li>
                ))}
              </ul>
            </Card>
          )}
        </Col>

        {/* Right Column - Sample Answer */}
        <Col span={12}>
          <Card 
            title="Sample Answer Demonstration"
            extra={
              <Space>
                <Button 
                  type="text"
                  icon={showSampleAnswer ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  onClick={() => setShowSampleAnswer(!showSampleAnswer)}
                  size="small"
                >
                  {showSampleAnswer ? 'Hide' : 'Show'} Full Answer
                </Button>
              </Space>
            }
            style={{ marginBottom: '20px' }}
          >
            {showSampleAnswer ? (
              <div 
                style={{ 
                  fontSize: '16px', 
                  lineHeight: '1.8',
                  fontFamily: 'Georgia, serif',
                  backgroundColor: '#fafafa',
                  padding: '20px',
                  borderRadius: '8px'
                }}
                dangerouslySetInnerHTML={{ __html: highlightVocabulary(sampleAnswer) }}
              />
            ) : (
              <div>
                <Alert
                  message="Reveal Sample Answer Step by Step"
                  description="Click 'Reveal Next Paragraph' to show the sample answer paragraph by paragraph for classroom demonstration."
                  type="info"
                  showIcon
                  style={{ marginBottom: '16px' }}
                />
                
                <Space style={{ marginBottom: '16px' }}>
                  <Button 
                    type="primary"
                    onClick={revealNextParagraph}
                    disabled={revealedParagraphs.length >= sampleParagraphs.length}
                  >
                    Reveal Next Paragraph ({revealedParagraphs.length + 1}/{sampleParagraphs.length})
                  </Button>
                  <Button onClick={resetReveal}>
                    Reset
                  </Button>
                </Space>

                <div style={{ 
                  fontSize: '16px', 
                  lineHeight: '1.8',
                  fontFamily: 'Georgia, serif',
                  backgroundColor: '#fafafa',
                  padding: '20px',
                  borderRadius: '8px',
                  minHeight: '300px'
                }}>
                  {revealedParagraphs.map(index => (
                    <div key={index} style={{ marginBottom: '16px' }}>
                      <div 
                        dangerouslySetInnerHTML={{ 
                          __html: highlightVocabulary(sampleParagraphs[index]) 
                        }} 
                      />
                      {index < revealedParagraphs.length - 1 && <Divider />}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </Card>

          {/* Vocabulary Helper */}
          {vocabularyHints.length > 0 && (
            <Card 
              title="Key Vocabulary"
              extra={
                <Button 
                  type="text"
                  icon={showVocabulary ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  onClick={() => setShowVocabulary(!showVocabulary)}
                  size="small"
                >
                  {showVocabulary ? 'Hide' : 'Show'}
                </Button>
              }
            >
              {showVocabulary && (
                <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {vocabularyHints.map((hint, index) => (
                    <div key={index} style={{ marginBottom: '12px', padding: '8px', backgroundColor: '#f9f9f9', borderRadius: '4px' }}>
                      <Text strong style={{ color: '#1890ff' }}>{hint.word}</Text>
                      <br />
                      <Text style={{ fontSize: '14px' }}>{hint.definition}</Text>
                      {hint.example && (
                        <>
                          <br />
                          <Text italic style={{ fontSize: '13px', color: '#666' }}>
                            Example: {hint.example}
                          </Text>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default WritingTeacherDemo;
