import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface ReadingMaterial {
  id: string;
  title: string;
  topic: string;
  level: string;
  content: string;
  questions: {
    id: string;
    question: string;
    type: 'multiple-choice' | 'true-false' | 'fill-blank' | 'short-answer';
    options?: string[];
    correctAnswer: string | string[];
  }[];
  timeLimit: number; // in minutes
  createdAt: Date;
  updatedAt: Date;
}

export interface ListeningMaterial {
  id: string;
  title: string;
  topic: string;
  level: string;
  audioUrl: string;
  transcript: string;
  questions: {
    id: string;
    question: string;
    type: 'multiple-choice' | 'true-false' | 'fill-blank' | 'short-answer';
    options?: string[];
    correctAnswer: string | string[];
    timestamp?: number; // when in audio this question relates to
  }[];
  timeLimit: number; // in minutes
  createdAt: Date;
  updatedAt: Date;
}

export interface WritingMaterial {
  id: string;
  type: 'task1' | 'task2';
  title: string;
  topic: string;
  level: string;
  prompt: string;
  instructions: string;
  timeLimit: number; // in minutes
  wordLimit: {
    min: number;
    max: number;
  };
  tips?: string[];
  sampleAnswer?: string;
  writingSteps?: string[];
  imageUrl?: string; // For Task 1 visual data
  createdAt: Date;
  updatedAt: Date;
}

export interface SpeakingMaterial {
  id: string;
  part: 1 | 2 | 3;
  title: string;
  topic: string;
  level: string;
  instructions: string;
  questions: string[];
  preparationTime?: number; // in seconds
  speakingTime: number; // in seconds
  tips?: string[];
  sampleResponses?: string[];
  speakingStrategies?: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface MaterialsContextType {
  // Reading Materials
  readingMaterials: ReadingMaterial[];
  addReadingMaterial: (material: Omit<ReadingMaterial, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateReadingMaterial: (id: string, material: Partial<ReadingMaterial>) => void;
  deleteReadingMaterial: (id: string) => void;
  getReadingMaterialsByTopic: (topic: string) => ReadingMaterial[];

  // Listening Materials
  listeningMaterials: ListeningMaterial[];
  addListeningMaterial: (material: Omit<ListeningMaterial, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateListeningMaterial: (id: string, material: Partial<ListeningMaterial>) => void;
  deleteListeningMaterial: (id: string) => void;
  getListeningMaterialsByTopic: (topic: string) => ListeningMaterial[];

  // Writing Materials
  writingMaterials: WritingMaterial[];
  addWritingMaterial: (material: Omit<WritingMaterial, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateWritingMaterial: (id: string, material: Partial<WritingMaterial>) => void;
  deleteWritingMaterial: (id: string) => void;
  getWritingMaterialsByTopic: (topic: string) => WritingMaterial[];

  // Speaking Materials
  speakingMaterials: SpeakingMaterial[];
  addSpeakingMaterial: (material: Omit<SpeakingMaterial, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateSpeakingMaterial: (id: string, material: Partial<SpeakingMaterial>) => void;
  deleteSpeakingMaterial: (id: string) => void;
  getSpeakingMaterialsByTopic: (topic: string) => SpeakingMaterial[];
}

const MaterialsContext = createContext<MaterialsContextType | undefined>(undefined);

export const useMaterials = () => {
  const context = useContext(MaterialsContext);
  if (!context) {
    throw new Error('useMaterials must be used within a MaterialsProvider');
  }
  return context;
};

interface MaterialsProviderProps {
  children: ReactNode;
}

export const MaterialsProvider: React.FC<MaterialsProviderProps> = ({ children }) => {
  const [readingMaterials, setReadingMaterials] = useState<ReadingMaterial[]>([]);
  const [listeningMaterials, setListeningMaterials] = useState<ListeningMaterial[]>([]);
  const [writingMaterials, setWritingMaterials] = useState<WritingMaterial[]>([]);
  const [speakingMaterials, setSpeakingMaterials] = useState<SpeakingMaterial[]>([]);

  // Reading Materials CRUD
  const addReadingMaterial = (material: Omit<ReadingMaterial, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newMaterial: ReadingMaterial = {
      ...material,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setReadingMaterials(prev => [...prev, newMaterial]);
  };

  const updateReadingMaterial = (id: string, updates: Partial<ReadingMaterial>) => {
    setReadingMaterials(prev =>
      prev.map(material =>
        material.id === id
          ? { ...material, ...updates, updatedAt: new Date() }
          : material
      )
    );
  };

  const deleteReadingMaterial = (id: string) => {
    setReadingMaterials(prev => prev.filter(material => material.id !== id));
  };

  const getReadingMaterialsByTopic = (topic: string) => {
    return readingMaterials.filter(material => material.topic === topic);
  };

  // Listening Materials CRUD
  const addListeningMaterial = (material: Omit<ListeningMaterial, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newMaterial: ListeningMaterial = {
      ...material,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setListeningMaterials(prev => [...prev, newMaterial]);
  };

  const updateListeningMaterial = (id: string, updates: Partial<ListeningMaterial>) => {
    setListeningMaterials(prev =>
      prev.map(material =>
        material.id === id
          ? { ...material, ...updates, updatedAt: new Date() }
          : material
      )
    );
  };

  const deleteListeningMaterial = (id: string) => {
    setListeningMaterials(prev => prev.filter(material => material.id !== id));
  };

  const getListeningMaterialsByTopic = (topic: string) => {
    return listeningMaterials.filter(material => material.topic === topic);
  };

  // Writing Materials CRUD
  const addWritingMaterial = (material: Omit<WritingMaterial, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newMaterial: WritingMaterial = {
      ...material,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setWritingMaterials(prev => [...prev, newMaterial]);
  };

  const updateWritingMaterial = (id: string, updates: Partial<WritingMaterial>) => {
    setWritingMaterials(prev =>
      prev.map(material =>
        material.id === id
          ? { ...material, ...updates, updatedAt: new Date() }
          : material
      )
    );
  };

  const deleteWritingMaterial = (id: string) => {
    setWritingMaterials(prev => prev.filter(material => material.id !== id));
  };

  const getWritingMaterialsByTopic = (topic: string) => {
    return writingMaterials.filter(material => material.topic === topic);
  };

  // Speaking Materials CRUD
  const addSpeakingMaterial = (material: Omit<SpeakingMaterial, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newMaterial: SpeakingMaterial = {
      ...material,
      id: Date.now().toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    setSpeakingMaterials(prev => [...prev, newMaterial]);
  };

  const updateSpeakingMaterial = (id: string, updates: Partial<SpeakingMaterial>) => {
    setSpeakingMaterials(prev =>
      prev.map(material =>
        material.id === id
          ? { ...material, ...updates, updatedAt: new Date() }
          : material
      )
    );
  };

  const deleteSpeakingMaterial = (id: string) => {
    setSpeakingMaterials(prev => prev.filter(material => material.id !== id));
  };

  const getSpeakingMaterialsByTopic = (topic: string) => {
    return speakingMaterials.filter(material => material.topic === topic);
  };

  const value: MaterialsContextType = {
    // Reading
    readingMaterials,
    addReadingMaterial,
    updateReadingMaterial,
    deleteReadingMaterial,
    getReadingMaterialsByTopic,

    // Listening
    listeningMaterials,
    addListeningMaterial,
    updateListeningMaterial,
    deleteListeningMaterial,
    getListeningMaterialsByTopic,

    // Writing
    writingMaterials,
    addWritingMaterial,
    updateWritingMaterial,
    deleteWritingMaterial,
    getWritingMaterialsByTopic,

    // Speaking
    speakingMaterials,
    addSpeakingMaterial,
    updateSpeakingMaterial,
    deleteSpeakingMaterial,
    getSpeakingMaterialsByTopic,
  };

  return (
    <MaterialsContext.Provider value={value}>
      {children}
    </MaterialsContext.Provider>
  );
};
