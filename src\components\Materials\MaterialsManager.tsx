import React, { useState } from 'react';
import { Card, Typography, Tabs, Button, Space, Modal, Form, Input, Select, InputNumber, List, Popconfirm, message } from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined,
  BookOutlined,
  SoundOutlined,
  EditFilled,
  AudioOutlined
} from '@ant-design/icons';
import { useMaterials, ReadingMaterial, ListeningMaterial, WritingMaterial, SpeakingMaterial } from '../../contexts/MaterialsContext';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Option } = Select;

const MaterialsManager: React.FC = () => {
  const {
    readingMaterials,
    addReadingMaterial,
    updateReadingMaterial,
    deleteReadingMaterial,
    listeningMaterials,
    addListeningMaterial,
    updateListeningMaterial,
    deleteListeningMaterial,
    writingMaterials,
    addWritingMaterial,
    updateWritingMaterial,
    deleteWritingMaterial,
    speakingMaterials,
    addSpeakingMaterial,
    updateSpeakingMaterial,
    deleteSpeakingMaterial,
  } = useMaterials();

  const [activeTab, setActiveTab] = useState('reading');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [form] = Form.useForm();

  const topics = ['Environment', 'Technology', 'Education', 'Health', 'Travel', 'Culture'];
  const levels = ['A1', 'A2', 'B1', 'B2', 'C1', 'C2'];

  const handleAdd = (type: string) => {
    setEditingItem(null);
    setActiveTab(type);
    setIsModalVisible(true);
    form.resetFields();
  };

  const handleEdit = (item: any, type: string) => {
    setEditingItem({ ...item, type });
    setActiveTab(type);
    setIsModalVisible(true);
    form.setFieldsValue(item);
  };

  const handleDelete = (id: string, type: string) => {
    switch (type) {
      case 'reading':
        deleteReadingMaterial(id);
        break;
      case 'listening':
        deleteListeningMaterial(id);
        break;
      case 'writing':
        deleteWritingMaterial(id);
        break;
      case 'speaking':
        deleteSpeakingMaterial(id);
        break;
    }
    message.success('Material deleted successfully');
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingItem) {
        // Update existing material
        switch (editingItem.type) {
          case 'reading':
            updateReadingMaterial(editingItem.id, values);
            break;
          case 'listening':
            updateListeningMaterial(editingItem.id, values);
            break;
          case 'writing':
            updateWritingMaterial(editingItem.id, values);
            break;
          case 'speaking':
            updateSpeakingMaterial(editingItem.id, values);
            break;
        }
        message.success('Material updated successfully');
      } else {
        // Add new material
        switch (activeTab) {
          case 'reading':
            addReadingMaterial(values);
            break;
          case 'listening':
            addListeningMaterial(values);
            break;
          case 'writing':
            addWritingMaterial(values);
            break;
          case 'speaking':
            addSpeakingMaterial(values);
            break;
        }
        message.success('Material added successfully');
      }
      
      setIsModalVisible(false);
      form.resetFields();
      setEditingItem(null);
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const renderMaterialList = (materials: any[], type: string) => (
    <List
      dataSource={materials}
      renderItem={(item) => (
        <List.Item
          actions={[
            <Button
              key="edit"
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(item, type)}
            >
              Edit
            </Button>,
            <Popconfirm
              key="delete"
              title="Are you sure you want to delete this material?"
              onConfirm={() => handleDelete(item.id, type)}
              okText="Yes"
              cancelText="No"
            >
              <Button type="text" danger icon={<DeleteOutlined />}>
                Delete
              </Button>
            </Popconfirm>,
          ]}
        >
          <List.Item.Meta
            title={item.title}
            description={
              <Space direction="vertical" size="small">
                <Text type="secondary">Topic: {item.topic} | Level: {item.level}</Text>
                <Text type="secondary">
                  {type === 'writing' && `Type: ${item.type?.toUpperCase()} | `}
                  {type === 'speaking' && `Part: ${item.part} | `}
                  Time: {item.timeLimit || item.speakingTime} {type === 'speaking' ? 'seconds' : 'minutes'}
                </Text>
                <Text type="secondary">
                  Created: {new Date(item.createdAt).toLocaleDateString()}
                </Text>
              </Space>
            }
          />
        </List.Item>
      )}
    />
  );

  const renderModalContent = () => {
    switch (activeTab) {
      case 'reading':
        return (
          <>
            <Form.Item name="title" label="Title" rules={[{ required: true }]}>
              <Input placeholder="Enter reading material title" />
            </Form.Item>
            <Form.Item name="topic" label="Topic" rules={[{ required: true }]}>
              <Select placeholder="Select topic">
                {topics.map(topic => (
                  <Option key={topic} value={topic}>{topic}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="level" label="Level" rules={[{ required: true }]}>
              <Select placeholder="Select level">
                {levels.map(level => (
                  <Option key={level} value={level}>{level}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="content" label="Content" rules={[{ required: true }]}>
              <TextArea rows={6} placeholder="Enter reading passage content" />
            </Form.Item>
            <Form.Item name="timeLimit" label="Time Limit (minutes)" rules={[{ required: true }]}>
              <InputNumber min={1} max={120} placeholder="30" />
            </Form.Item>
          </>
        );

      case 'listening':
        return (
          <>
            <Form.Item name="title" label="Title" rules={[{ required: true }]}>
              <Input placeholder="Enter listening material title" />
            </Form.Item>
            <Form.Item name="topic" label="Topic" rules={[{ required: true }]}>
              <Select placeholder="Select topic">
                {topics.map(topic => (
                  <Option key={topic} value={topic}>{topic}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="level" label="Level" rules={[{ required: true }]}>
              <Select placeholder="Select level">
                {levels.map(level => (
                  <Option key={level} value={level}>{level}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="audioUrl" label="Audio URL" rules={[{ required: true }]}>
              <Input placeholder="Enter audio file URL" />
            </Form.Item>
            <Form.Item name="transcript" label="Transcript" rules={[{ required: true }]}>
              <TextArea rows={6} placeholder="Enter audio transcript" />
            </Form.Item>
            <Form.Item name="timeLimit" label="Time Limit (minutes)" rules={[{ required: true }]}>
              <InputNumber min={1} max={120} placeholder="30" />
            </Form.Item>
          </>
        );

      case 'writing':
        return (
          <>
            <Form.Item name="title" label="Title" rules={[{ required: true }]}>
              <Input placeholder="Enter writing task title" />
            </Form.Item>
            <Form.Item name="type" label="Task Type" rules={[{ required: true }]}>
              <Select placeholder="Select task type">
                <Option value="task1">Task 1</Option>
                <Option value="task2">Task 2</Option>
              </Select>
            </Form.Item>
            <Form.Item name="topic" label="Topic" rules={[{ required: true }]}>
              <Select placeholder="Select topic">
                {topics.map(topic => (
                  <Option key={topic} value={topic}>{topic}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="level" label="Level" rules={[{ required: true }]}>
              <Select placeholder="Select level">
                {levels.map(level => (
                  <Option key={level} value={level}>{level}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="prompt" label="Prompt" rules={[{ required: true }]}>
              <TextArea rows={4} placeholder="Enter writing task prompt" />
            </Form.Item>
            <Form.Item name="instructions" label="Instructions" rules={[{ required: true }]}>
              <TextArea rows={3} placeholder="Enter task instructions" />
            </Form.Item>
            <Form.Item name="timeLimit" label="Time Limit (minutes)" rules={[{ required: true }]}>
              <InputNumber min={1} max={120} placeholder="40" />
            </Form.Item>
          </>
        );

      case 'speaking':
        return (
          <>
            <Form.Item name="title" label="Title" rules={[{ required: true }]}>
              <Input placeholder="Enter speaking task title" />
            </Form.Item>
            <Form.Item name="part" label="Speaking Part" rules={[{ required: true }]}>
              <Select placeholder="Select speaking part">
                <Option value={1}>Part 1</Option>
                <Option value={2}>Part 2</Option>
                <Option value={3}>Part 3</Option>
              </Select>
            </Form.Item>
            <Form.Item name="topic" label="Topic" rules={[{ required: true }]}>
              <Select placeholder="Select topic">
                {topics.map(topic => (
                  <Option key={topic} value={topic}>{topic}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="level" label="Level" rules={[{ required: true }]}>
              <Select placeholder="Select level">
                {levels.map(level => (
                  <Option key={level} value={level}>{level}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item name="instructions" label="Instructions" rules={[{ required: true }]}>
              <TextArea rows={3} placeholder="Enter speaking instructions" />
            </Form.Item>
            <Form.Item name="speakingTime" label="Speaking Time (seconds)" rules={[{ required: true }]}>
              <InputNumber min={30} max={300} placeholder="120" />
            </Form.Item>
            <Form.Item name="preparationTime" label="Preparation Time (seconds)">
              <InputNumber min={0} max={120} placeholder="60" />
            </Form.Item>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <Card>
        <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            <BookOutlined /> Materials Manager
          </Title>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={<span><BookOutlined />Reading</span>} 
            key="reading"
          >
            <div style={{ marginBottom: '16px' }}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => handleAdd('reading')}
              >
                Add Reading Material
              </Button>
            </div>
            {renderMaterialList(readingMaterials, 'reading')}
          </TabPane>

          <TabPane 
            tab={<span><AudioOutlined />Listening</span>} 
            key="listening"
          >
            <div style={{ marginBottom: '16px' }}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => handleAdd('listening')}
              >
                Add Listening Material
              </Button>
            </div>
            {renderMaterialList(listeningMaterials, 'listening')}
          </TabPane>

          <TabPane 
            tab={<span><EditFilled />Writing</span>} 
            key="writing"
          >
            <div style={{ marginBottom: '16px' }}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => handleAdd('writing')}
              >
                Add Writing Material
              </Button>
            </div>
            {renderMaterialList(writingMaterials, 'writing')}
          </TabPane>

          <TabPane 
            tab={<span><SoundOutlined />Speaking</span>} 
            key="speaking"
          >
            <div style={{ marginBottom: '16px' }}>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => handleAdd('speaking')}
              >
                Add Speaking Material
              </Button>
            </div>
            {renderMaterialList(speakingMaterials, 'speaking')}
          </TabPane>
        </Tabs>

        <Modal
          title={`${editingItem ? 'Edit' : 'Add'} ${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Material`}
          open={isModalVisible}
          onOk={handleSubmit}
          onCancel={() => {
            setIsModalVisible(false);
            setEditingItem(null);
            form.resetFields();
          }}
          width={600}
        >
          <Form form={form} layout="vertical">
            {renderModalContent()}
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default MaterialsManager;
