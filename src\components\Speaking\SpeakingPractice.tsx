import React, { useState, useEffect, useRef } from 'react';
import { Card, Typography, Button, Space, Progress, Row, Col, Divider, Tag, Alert } from 'antd';
import { 
  AudioOutlined, 
  PlayCircleOutlined, 
  PauseCircleOutlined,
  StopOutlined,
  ClockCircleOutlined,
  SoundOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface SpeakingTask {
  id: string;
  part: 1 | 2 | 3;
  title: string;
  instructions: string;
  questions: string[];
  preparationTime?: number; // in seconds
  speakingTime: number; // in seconds
  tips?: string[];
}

interface VocabularyHint {
  word: string;
  definition: string;
  example?: string;
}

interface SpeakingPracticeProps {
  task: SpeakingTask;
  topic: string;
  vocabularyHints?: VocabularyHint[];
}

const SpeakingPractice: React.FC<SpeakingPracticeProps> = ({
  task,
  topic,
  vocabularyHints = []
}) => {
  const [currentPhase, setCurrentPhase] = useState<'instructions' | 'preparation' | 'speaking' | 'completed'>('instructions');
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [showVocabulary, setShowVocabulary] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [recordingSupported, setRecordingSupported] = useState(false);
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);

  // Check if recording is supported
  useEffect(() => {
    setRecordingSupported(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
  }, []);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            handlePhaseComplete();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [timeRemaining]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const startPreparation = () => {
    if (task.preparationTime) {
      setCurrentPhase('preparation');
      setTimeRemaining(task.preparationTime);
    } else {
      startSpeaking();
    }
  };

  const startSpeaking = () => {
    setCurrentPhase('speaking');
    setTimeRemaining(task.speakingTime);
    setCurrentQuestionIndex(0);
  };

  const handlePhaseComplete = () => {
    if (currentPhase === 'preparation') {
      startSpeaking();
    } else if (currentPhase === 'speaking') {
      if (currentQuestionIndex < task.questions.length - 1) {
        setCurrentQuestionIndex(prev => prev + 1);
        setTimeRemaining(task.speakingTime);
      } else {
        setCurrentPhase('completed');
        stopRecording();
      }
    }
  };

  const startRecording = async () => {
    if (!recordingSupported) return;

    try {
      const constraints: MediaStreamConstraints = { audio: true };
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.start();
      setIsRecording(true);
    } catch (error) {
      console.error('Error starting recording:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
    }
  };

  const resetPractice = () => {
    setCurrentPhase('instructions');
    setTimeRemaining(0);
    setCurrentQuestionIndex(0);
    stopRecording();
  };

  const getPhaseTitle = () => {
    switch (currentPhase) {
      case 'instructions': return 'Instructions';
      case 'preparation': return 'Preparation Time';
      case 'speaking': return `Speaking - Question ${currentQuestionIndex + 1}`;
      case 'completed': return 'Practice Completed';
      default: return '';
    }
  };

  const getTimeColor = () => {
    const totalTime = currentPhase === 'preparation' ? task.preparationTime || 0 : task.speakingTime;
    const percentage = (timeRemaining / totalTime) * 100;
    if (percentage <= 10) return '#ff4d4f';
    if (percentage <= 25) return '#faad14';
    return '#52c41a';
  };

  return (
    <div style={{ padding: '20px' }}>
      {/* Header */}
      <Card style={{ marginBottom: '20px' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              <SoundOutlined /> IELTS Speaking Part {task.part} - {topic}
            </Title>
            <Text type="secondary">{task.title}</Text>
          </Col>
          <Col>
            <Space size="large">
              {/* Timer */}
              {timeRemaining > 0 && (
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: getTimeColor() }}>
                    <ClockCircleOutlined /> {formatTime(timeRemaining)}
                  </div>
                  <Text type="secondary">{getPhaseTitle()}</Text>
                </div>
              )}

              {/* Recording Status */}
              {recordingSupported && (
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '20px', color: isRecording ? '#ff4d4f' : '#666' }}>
                    <AudioOutlined /> {isRecording ? 'Recording...' : 'Ready to Record'}
                  </div>
                  {currentPhase === 'speaking' && (
                    <Space>
                      <Button 
                        type="primary" 
                        danger={isRecording}
                        icon={isRecording ? <StopOutlined /> : <AudioOutlined />}
                        onClick={isRecording ? stopRecording : startRecording}
                        size="small"
                      >
                        {isRecording ? 'Stop' : 'Record'}
                      </Button>
                    </Space>
                  )}
                </div>
              )}

              {/* Reset Button */}
              <Button 
                icon={<ReloadOutlined />} 
                onClick={resetPractice}
                size="small"
              >
                Reset
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Row gutter={24}>
        {/* Left Column - Instructions and Questions */}
        <Col span={16}>
          {currentPhase === 'instructions' && (
            <Card title="Instructions" style={{ marginBottom: '20px' }}>
              <Paragraph style={{ fontSize: '16px', lineHeight: '1.6' }}>
                {task.instructions}
              </Paragraph>
              
              {task.tips && task.tips.length > 0 && (
                <div>
                  <Divider />
                  <Title level={5}>Tips:</Title>
                  <ul>
                    {task.tips.map((tip, index) => (
                      <li key={index} style={{ marginBottom: '8px' }}>
                        <Text>{tip}</Text>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div style={{ marginTop: '20px', textAlign: 'center' }}>
                <Button 
                  type="primary" 
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={startPreparation}
                >
                  Start Practice
                </Button>
              </div>
            </Card>
          )}

          {(currentPhase === 'preparation' || currentPhase === 'speaking') && (
            <Card 
              title={currentPhase === 'preparation' ? 'Preparation Time' : `Question ${currentQuestionIndex + 1}`}
              style={{ marginBottom: '20px' }}
            >
              {currentPhase === 'preparation' && (
                <div>
                  <Alert 
                    message="Preparation Time" 
                    description="Use this time to think about your answers. You can make notes if needed."
                    type="info" 
                    style={{ marginBottom: '20px' }}
                  />
                  <Title level={4}>Questions you will be asked:</Title>
                  <ol>
                    {task.questions.map((question, index) => (
                      <li key={index} style={{ marginBottom: '8px' }}>
                        <Text style={{ fontSize: '16px' }}>{question}</Text>
                      </li>
                    ))}
                  </ol>
                </div>
              )}

              {currentPhase === 'speaking' && (
                <div>
                  <div style={{ 
                    padding: '20px', 
                    backgroundColor: '#f0f2f5', 
                    borderRadius: '8px',
                    marginBottom: '20px'
                  }}>
                    <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
                      {task.questions[currentQuestionIndex]}
                    </Title>
                  </div>
                  
                  {!recordingSupported && (
                    <Alert 
                      message="Recording not supported" 
                      description="Your browser doesn't support audio recording. You can still practice speaking aloud."
                      type="warning" 
                      style={{ marginBottom: '20px' }}
                    />
                  )}

                  <div style={{ textAlign: 'center' }}>
                    <Text style={{ fontSize: '16px' }}>
                      Speak clearly and try to use the full time available.
                    </Text>
                  </div>
                </div>
              )}
            </Card>
          )}

          {currentPhase === 'completed' && (
            <Card title="Practice Completed" style={{ marginBottom: '20px' }}>
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Title level={4} style={{ color: '#52c41a' }}>
                  Well done! You've completed the speaking practice.
                </Title>
                <Paragraph style={{ fontSize: '16px' }}>
                  Review your performance and consider practicing again to improve your fluency and confidence.
                </Paragraph>
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />}
                  onClick={resetPractice}
                  size="large"
                >
                  Practice Again
                </Button>
              </div>
            </Card>
          )}
        </Col>

        {/* Right Column - Vocabulary Helper */}
        <Col span={8}>
          {vocabularyHints.length > 0 && (
            <Card 
              title="Vocabulary Helper"
              extra={
                <Button 
                  type="text"
                  icon={showVocabulary ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  onClick={() => setShowVocabulary(!showVocabulary)}
                >
                  {showVocabulary ? 'Hide' : 'Show'}
                </Button>
              }
            >
              {showVocabulary && (
                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                  {vocabularyHints.map((hint, index) => (
                    <div key={index} style={{ 
                      marginBottom: '12px', 
                      padding: '8px', 
                      backgroundColor: '#f9f9f9', 
                      borderRadius: '4px' 
                    }}>
                      <Text strong style={{ color: '#1890ff' }}>{hint.word}</Text>
                      <br />
                      <Text style={{ fontSize: '14px' }}>{hint.definition}</Text>
                      {hint.example && (
                        <>
                          <br />
                          <Text italic style={{ fontSize: '13px', color: '#666' }}>
                            Example: {hint.example}
                          </Text>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default SpeakingPractice;
