import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface VocabularyItem {
  id: string;
  word: string;
  definition: string;
  example: string;
  topic: string;
  level: string;
  pronunciation?: string;
  synonyms?: string[];
}

export interface VocabularyHint {
  word: string;
  definition: string;
  example?: string;
}

interface VocabularyContextType {
  vocabularyList: VocabularyItem[];
  setVocabularyList: React.Dispatch<React.SetStateAction<VocabularyItem[]>>;
  getVocabularyByTopic: (topic: string) => VocabularyHint[];
  getAllVocabulary: () => VocabularyHint[];
  addVocabulary: (item: Omit<VocabularyItem, 'id'>) => void;
  updateVocabulary: (id: string, item: Partial<VocabularyItem>) => void;
  deleteVocabulary: (id: string) => void;
}

const VocabularyContext = createContext<VocabularyContextType | undefined>(undefined);

export const useVocabulary = () => {
  const context = useContext(VocabularyContext);
  if (!context) {
    throw new Error('useVocabulary must be used within a VocabularyProvider');
  }
  return context;
};

interface VocabularyProviderProps {
  children: ReactNode;
}

export const VocabularyProvider: React.FC<VocabularyProviderProps> = ({ children }) => {
  const [vocabularyList, setVocabularyList] = useState<VocabularyItem[]>([]);

  // Initialize with sample vocabulary data
  useEffect(() => {
    const sampleVocabulary: VocabularyItem[] = [
      {
        id: '1',
        word: 'sustainable',
        definition: 'able to be maintained at a certain rate or level without depleting natural resources',
        example: 'The company adopted sustainable business practices to reduce environmental impact.',
        topic: 'Environment',
        level: 'B2+',
        pronunciation: '/səˈsteɪnəbl/',
        synonyms: ['eco-friendly', 'renewable', 'viable']
      },
      {
        id: '2',
        word: 'innovation',
        definition: 'the action or process of innovating; a new method, idea, product, etc.',
        example: 'Technological innovation has transformed the way we communicate.',
        topic: 'Technology',
        level: 'C1',
        pronunciation: '/ˌɪnəˈveɪʃn/',
        synonyms: ['advancement', 'breakthrough', 'development']
      },
      {
        id: '3',
        word: 'curriculum',
        definition: 'the subjects comprising a course of study in a school or college',
        example: 'The new curriculum includes more emphasis on critical thinking skills.',
        topic: 'Education',
        level: 'B2',
        pronunciation: '/kəˈrɪkjələm/',
        synonyms: ['syllabus', 'program', 'course']
      },
      {
        id: '4',
        word: 'biodiversity',
        definition: 'the variety of plant and animal life in the world or in a particular habitat',
        example: 'The rainforest is known for its incredible biodiversity.',
        topic: 'Environment',
        level: 'C1',
        pronunciation: '/ˌbaɪoʊdaɪˈvɜrsəti/',
        synonyms: ['biological diversity', 'ecological variety']
      },
      {
        id: '5',
        word: 'carbon footprint',
        definition: 'the amount of carbon dioxide released into the atmosphere as a result of activities',
        example: 'Flying frequently increases your carbon footprint significantly.',
        topic: 'Environment',
        level: 'B2+',
        pronunciation: '/ˈkɑrbən ˈfʊtprɪnt/',
        synonyms: ['carbon emissions', 'environmental impact']
      }
    ];
    setVocabularyList(sampleVocabulary);
  }, []);

  const getVocabularyByTopic = (topic: string): VocabularyHint[] => {
    return vocabularyList
      .filter(item => item.topic === topic)
      .map(item => ({
        word: item.word,
        definition: item.definition,
        example: item.example
      }));
  };

  const getAllVocabulary = (): VocabularyHint[] => {
    return vocabularyList.map(item => ({
      word: item.word,
      definition: item.definition,
      example: item.example
    }));
  };

  const addVocabulary = (item: Omit<VocabularyItem, 'id'>) => {
    const newItem: VocabularyItem = {
      id: Date.now().toString(),
      ...item
    };
    setVocabularyList(prev => [...prev, newItem]);
  };

  const updateVocabulary = (id: string, updates: Partial<VocabularyItem>) => {
    setVocabularyList(prev => 
      prev.map(item => 
        item.id === id ? { ...item, ...updates } : item
      )
    );
  };

  const deleteVocabulary = (id: string) => {
    setVocabularyList(prev => prev.filter(item => item.id !== id));
  };

  const value: VocabularyContextType = {
    vocabularyList,
    setVocabularyList,
    getVocabularyByTopic,
    getAllVocabulary,
    addVocabulary,
    updateVocabulary,
    deleteVocabulary
  };

  return (
    <VocabularyContext.Provider value={value}>
      {children}
    </VocabularyContext.Provider>
  );
};
